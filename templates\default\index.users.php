<?php
/**
 * AionCMS
 * https://aioncms.com/
 *
 * @version 4.0
 * <AUTHOR> <http://lautaroangelico.com/>
 * @copyright (c) 2012-2020 Lautaro Angelico, All Rights Reserved
 */

/**
 *
 * For support join us on discord:
 * https://aioncms.com/discord
 *
 */

if(!defined('access') or !access) die();
?>
<!DOCTYPE HTML>
<html>

<head>
<meta charset="utf-8" />
    <meta http-equiv="Content-type" content="text/html; charset=utf-8" />
    <meta property="og:title" content="OneAion - perfect 4.6 server" />
    <meta property="og:description" content="OneAion - perfect 4.6 serve, join us!" />
    <meta http-equiv="Content-type" content="text/html; charset=utf-8" />
    <meta property="og:image" content="" />
    <meta name="description" content="OneAion - perfect 4.6 serve, join us!">
    <?php
    // Inclure le script de protection StarPass uniquement pour la page donate/success
    if(isset($_GET['request']) && $_GET['request'] == 'donate/success') {
        include_once(__PATH_TEMPLATE_ROOT__ . 'header.success.php');
    }
    ?>
    <meta name="robots" content="index, follow">
    <title><?php echo config('website_title'); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="icon" type="image/ico" href="<?php template_img(); ?>favicon/fav.ico">
    <link rel="stylesheet" href="<?php template_css(); ?>core.css?version=<?php echo filemtime('templates/default/css/core.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>datepicker.css?version=<?php echo filemtime('templates/default/css/datepicker.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>iconfont.css?version=<?php echo filemtime('templates/default/css/iconfont.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>flag-icon.min.css">
    <link rel="stylesheet" href="<?php template_css(); ?>style.css?version=<?php echo filemtime('templates/default/css/style.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>cp.css?version=<?php echo filemtime('templates/default/css/cp.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>added.css">
    <link rel="stylesheet" href="<?php template_css(); ?>dataTables.bootstrap4.css">
    <link rel="stylesheet" href="<?php template_css(); ?>jquery.adb-syndication-2.4.2.css?version=<?php echo filemtime('templates/default/css/jquery.adb-syndication-2.4.2.css') ?>">
    <!-- Golden Theme CSS - MUST BE LAST TO OVERRIDE ALL OTHER STYLES -->
    <link rel="stylesheet" href="<?php template_css(); ?>golden-theme.css?version=<?php echo filemtime('templates/default/css/golden-theme.css') ?>">
    <script src="<?php template_js() ?>jquery.adb-syndication.min-2.4.2.js"></script>
    <script>
    var baseUrl = '<?php echo __BASE_URL__; ?>';
    </script>
    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/id_ID/sdk.js#xfbml=1&version=v10.0"
        nonce="ib9T2pWj"></script>
</head>

<body>
    <div class="main-wrapper">
        <div class="horizontal-menu">
            <?php require('header.php') ?>
        </div>
        <div class="page-wrapper">
            <div class="page-content">
                <?php Handler::loadContent(); ?>

                <footer class="footer d-flex flex-column flex-md-row align-items-center justify-content-between">
                    <p class="text-muted text-center text-md-left">
                        Elyon Aion 2025. <br>
                        <small>Elyon Aion is not the official server of the game, it is intended solely for familiarization with the game Aion. All rights to the Aion trademark belong to the company-owner.</small>
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src='https://www.google.com/recaptcha/api.js'></script>
	<script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <script src="<?php template_js() ?>core.js"></script>
    <script src="<?php template_js() ?>feather.min.js"></script>
    <script src="<?php template_js() ?>template.js"></script>
    <script src="<?php template_js() ?>zooming.min.js"></script>
    <script src="<?php template_js() ?>bootstrap-datepicker.js"></script>
    <script>
        new Zooming().listen('img')
    </script>

    <!-- Golden Theme for User Dashboard -->
    <script>
    $(document).ready(function() {
        // Apply golden theme to dashboard elements
        function applyDashboardGoldenTheme() {
            // Fix white backgrounds
            $('.page-content *').each(function() {
                var $this = $(this);
                var computedStyle = window.getComputedStyle(this);
                var backgroundColor = computedStyle.backgroundColor;
                var color = computedStyle.color;

                // If background is white or very light, make it dark golden
                if (backgroundColor === 'rgb(255, 255, 255)' || backgroundColor === 'rgba(255, 255, 255, 1)' ||
                    backgroundColor === '#fff' || backgroundColor === '#ffffff' ||
                    backgroundColor === 'white') {
                    $this.css({
                        'background': 'linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%)',
                        'border': '1px solid #8b5a3c'
                    });
                }

                // If text is black or very dark, make it golden
                if (color === 'rgb(0, 0, 0)' || color === 'rgba(0, 0, 0, 1)' ||
                    color === 'rgb(34, 34, 34)' || color === 'rgb(51, 51, 51)') {
                    $this.css('color', '#f4e4a6');
                }
            });

            // Force specific elements to have dark backgrounds
            $('.page-content .card, .page-content .panel, .page-content .widget, .page-content .info-box, .page-content .stats-box').css({
                'background': 'linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%)',
                'border': '1px solid #8b5a3c',
                'color': '#f4e4a6'
            });

            // Ensure headers are golden
            $('.page-content h1, .page-content h2, .page-content h3, .page-content h4, .page-content h5, .page-content h6').css('color', '#d4af37');

            // Ensure links are golden
            $('.page-content a').css('color', '#d4af37');

            // Ensure labels are golden
            $('.page-content label').css('color', '#f4e4a6');

            // Fix any remaining white backgrounds with specific selectors
            $('.page-content [style*="background-color: #fff"], .page-content [style*="background: #fff"], .page-content [style*="background-color: white"]').css({
                'background': 'linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%)',
                'color': '#f4e4a6'
            });

            // Fix top navigation bar (logo and user menu)
            $('.horizontal-menu .navbar.top-navbar').css({
                'background': 'linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%)',
                'border-bottom': '2px solid #8b5a3c'
            });

            // Fix bottom navigation bar (main tabs)
            $('.horizontal-menu .bottom-navbar').css({
                'background': 'linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%)',
                'border-bottom': '2px solid #8b5a3c'
            });

            // Fix navigation links colors
            $('.horizontal-menu .navbar.top-navbar .nav-link, .horizontal-menu .bottom-navbar .nav-link').css('color', '#f4e4a6');

            // Fix active navigation items
            $('.horizontal-menu .bottom-navbar .nav-item.active .nav-link').css({
                'background': 'linear-gradient(135deg, #b8941f 0%, #d4af37 100%)',
                'color': '#0d0d0d'
            });
        }

        // Apply immediately
        applyDashboardGoldenTheme();

        // Apply again after short delays
        setTimeout(applyDashboardGoldenTheme, 100);
        setTimeout(applyDashboardGoldenTheme, 300);
        setTimeout(applyDashboardGoldenTheme, 500);
    });
    </script>
</body>

</html>