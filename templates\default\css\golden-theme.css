/* Golden Theme Enhancements */

/* Background texture overlay */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../img/golden-texture.svg') repeat;
  opacity: 0.3;
  z-index: -1;
  pointer-events: none;
}

/* Additional golden styling for cards and containers */
.card {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border: 1px solid var(--gold-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.card-header {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  color: var(--darker-bg);
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  border-bottom: 2px solid var(--gold-border);
}

.card-body {
  color: var(--light-gold-text);
}

/* Table styling */
.table {
  background: rgba(26, 26, 26, 0.9);
  color: var(--light-gold-text);
}

.table thead th {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  color: var(--darker-bg);
  border-color: var(--gold-border);
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.table tbody tr {
  border-color: var(--gold-border);
}

.table tbody tr:hover {
  background: rgba(212, 175, 55, 0.1);
}

/* Navigation enhancements */
.navbar, .top-navbar, .bottom-navbar {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border-bottom: 2px solid var(--gold-border);
  backdrop-filter: blur(10px);
}

.navbar-brand {
  color: var(--primary-gold) !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.navbar-brand img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);
}

.nav-link {
  color: var(--light-gold-text) !important;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-gold) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  transform: translateY(-1px);
}

.nav-link:hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
}

.nav-item.active .nav-link {
  color: var(--primary-gold) !important;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 5px;
  box-shadow: inset 0 0 10px rgba(212, 175, 55, 0.3);
}

.navbar-toggler {
  border-color: var(--gold-border);
  color: var(--primary-gold);
}

.navbar-toggler:hover {
  background: rgba(212, 175, 55, 0.2);
}

/* Page navigation specific styling */
.page-navigation {
  padding: 10px 0;
}

.page-navigation .nav-item {
  margin: 0 5px;
}

.page-navigation .nav-link {
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-navigation .nav-link .link-icon {
  width: 16px;
  height: 16px;
}

.page-navigation .nav-item:hover .nav-link {
  background: rgba(212, 175, 55, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Form enhancements */
.form-control {
  background: rgba(13, 13, 13, 0.9);
  border: 2px solid var(--gold-border);
  color: var(--light-gold-text);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.4), inset 0 0 5px rgba(212, 175, 55, 0.1);
  background: rgba(13, 13, 13, 1);
  outline: none;
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: var(--dark-gold);
  background: rgba(13, 13, 13, 0.95);
}

.form-control::placeholder {
  color: rgba(244, 228, 166, 0.6);
  font-style: italic;
}

.form-control:invalid {
  border-color: #f44336;
  box-shadow: 0 0 5px rgba(244, 67, 54, 0.3);
}

.form-control:valid {
  border-color: var(--primary-gold);
}

/* Form group styling */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  color: var(--light-gold-text) !important;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Form text and help text */
.form-text {
  color: rgba(244, 228, 166, 0.8) !important;
  font-size: 12px;
  margin-top: 5px;
  font-style: italic;
}

/* Checkbox styling */
.form-check {
  margin: 1rem 0;
}

.form-check-label {
  color: var(--light-gold-text) !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  background: rgba(13, 13, 13, 0.9);
  border: 2px solid var(--gold-border);
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  transition: all 0.3s ease;
}

.form-check-input:checked {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-color: var(--primary-gold);
}

.form-check-input:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--darker-bg);
  font-weight: bold;
  font-size: 12px;
}

.form-check-input:focus {
  box-shadow: 0 0 5px var(--primary-gold);
  outline: none;
}

/* Forms sample class */
.forms-sample {
  background: transparent;
}

/* Submit button in forms */
.forms-sample .btn {
  margin-top: 1rem;
  min-width: 150px;
}

/* Alert styling */
.alert {
  border: 1px solid var(--gold-border);
  background: rgba(26, 26, 26, 0.9);
  color: var(--light-gold-text);
}

.alert-success {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
  color: #a5d6a7;
}

.alert-danger {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #ef9a9a;
}

.alert-warning {
  background: rgba(255, 193, 7, 0.2);
  border-color: var(--primary-gold);
  color: var(--light-gold-text);
}

/* Badge styling */
.badge {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  color: var(--darker-bg);
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Pagination styling */
.pagination .page-link {
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid var(--gold-border);
  color: var(--light-gold-text);
}

.pagination .page-link:hover {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-color: var(--gold-border);
  color: var(--darker-bg);
}

/* Modal styling */
.modal-content {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%);
  border: 2px solid var(--gold-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.modal-header {
  border-bottom: 2px solid var(--gold-border);
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
}

.modal-title {
  color: var(--darker-bg);
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.modal-body {
  color: var(--light-gold-text);
}

.modal-footer {
  border-top: 1px solid var(--gold-border);
}

/* Dropdown styling */
.dropdown-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%);
  border: 1px solid var(--gold-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  color: var(--light-gold-text);
}

.dropdown-item:hover {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
}

/* Progress bar styling */
.progress {
  background: rgba(13, 13, 13, 0.9);
  border: 1px solid var(--gold-border);
}

.progress-bar {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
}

/* List group styling */
.list-group-item {
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid var(--gold-border);
  color: var(--light-gold-text);
}

.list-group-item:hover {
  background: rgba(212, 175, 55, 0.1);
}

.list-group-item.active {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-color: var(--gold-border);
  color: var(--darker-bg);
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%);
}

/* Text selection styling */
::selection {
  background: var(--primary-gold);
  color: var(--darker-bg);
}

::-moz-selection {
  background: var(--primary-gold);
  color: var(--darker-bg);
}

/* Footer styling */
.footer {
  background: linear-gradient(135deg, rgba(13, 13, 13, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%);
  border-top: 2px solid var(--gold-border);
  color: var(--light-gold-text);
}

/* Additional utility classes */
.text-gold {
  color: var(--primary-gold) !important;
}

.text-light-gold {
  color: var(--light-gold-text) !important;
}

.text-dark-gold {
  color: var(--dark-gold) !important;
}

.bg-gold {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
}

.bg-dark-gold {
  background: var(--dark-bg) !important;
}

.border-gold {
  border-color: var(--gold-border) !important;
}

/* Content area specific styling */
.auth-form-wrapper {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border: 1px solid var(--gold-border);
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
  backdrop-filter: blur(10px);
}

.auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
}

.noble-ui-logo img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);
  max-width: 155px !important;
  height: auto !important;
  width: auto !important;
  object-fit: contain !important;
  aspect-ratio: auto !important;
}

/* Prevent any image stretching in auth forms */
.auth-form-wrapper img {
  max-width: 155px !important;
  height: auto !important;
  width: auto !important;
  object-fit: contain !important;
}

/* Hero section styling */
#hero-section {
  position: relative;
  overflow: hidden;
}

.hero-overlay {
  background: linear-gradient(135deg, rgba(44, 24, 16, 0.7) 0%, rgba(26, 26, 26, 0.8) 100%);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-caption {
  position: relative;
  z-index: 2;
  color: var(--light-gold-text);
}

.hero-caption h1 {
  color: var(--primary-gold);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

/* Container and content styling */
.container {
  position: relative;
  z-index: 1;
}

/* Form labels */
label {
  color: var(--light-gold-text) !important;
  font-weight: 500;
}

/* Heading styling */
.heading h1 {
  color: var(--primary-gold);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.colored {
  color: var(--gold-hover) !important;
}

/* Animation elements */
.animation-element {
  opacity: 0.9;
}

/* HR styling */
hr {
  border-color: var(--gold-border);
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
  height: 2px;
  border: none;
}

/* Text muted override */
.text-muted {
  color: rgba(244, 228, 166, 0.7) !important;
}

/* Font weight overrides */
.font-weight-normal {
  color: var(--light-gold-text);
}

.font-weight-medium {
  color: var(--light-gold-text);
}

/* Small text styling */
small {
  color: rgba(244, 228, 166, 0.8);
}

/* Link styling in content areas */
.auth-form-wrapper a {
  color: var(--primary-gold);
  text-decoration: none;
}

.auth-form-wrapper a:hover {
  color: var(--gold-hover);
  text-decoration: underline;
}

/* Video overlay for hero sections */
#bgvid {
  filter: brightness(0.7) sepia(0.3) hue-rotate(35deg);
}

/* Page content wrapper */
.page-content {
  background: rgba(26, 26, 26, 0.3);
  min-height: calc(100vh - 200px);
  backdrop-filter: blur(5px);
}

/* Main wrapper styling */
.main-wrapper {
  background: var(--gold-gradient);
  min-height: 100vh;
}

/* Additional content containers */
.content-wrapper {
  background: rgba(26, 26, 26, 0.8);
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
  border: 1px solid var(--gold-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Login Page Specific Styling */
.auth-form-wrapper {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 20px rgba(212, 175, 55, 0.15) !important;
  backdrop-filter: blur(15px) !important;
  padding: 2rem !important;
}

.auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
  font-weight: 600 !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

.auth-form-wrapper .noble-ui-logo img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5) !important;
}

/* Login form labels */
.auth-form-wrapper label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  display: block !important;
}

/* Login form inputs */
.auth-form-wrapper .form-control {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 10px !important;
  padding: 15px 20px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.auth-form-wrapper .form-control:focus {
  border-color: var(--primary-gold) !important;
  box-shadow:
    0 0 15px rgba(212, 175, 55, 0.4),
    inset 0 0 10px rgba(212, 175, 55, 0.1) !important;
  background: rgba(13, 13, 13, 1) !important;
  transform: translateY(-2px) !important;
}

.auth-form-wrapper .form-control::placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

/* Login button */
.auth-form-wrapper .btn-primary {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 50%, var(--gold-hover) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 12px !important;
  color: var(--darker-bg) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  padding: 12px 30px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3) !important;
}

.auth-form-wrapper .btn-primary:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4) !important;
  border-color: var(--gold-hover) !important;
}

/* Links in login form */
.auth-form-wrapper a {
  color: var(--primary-gold) !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

.auth-form-wrapper a:hover {
  color: var(--gold-hover) !important;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5) !important;
}

/* Form groups */
.auth-form-wrapper .form-group {
  margin-bottom: 1.5rem !important;
}

/* Text elements */
.auth-form-wrapper p,
.auth-form-wrapper span,
.auth-form-wrapper div {
  color: var(--light-gold-text) !important;
}

/* Override any remaining black text */
.auth-form-wrapper * {
  color: inherit !important;
}

.auth-form-wrapper *[style*="color: #000"],
.auth-form-wrapper *[style*="color: black"],
.auth-form-wrapper *[style*="color: #222"],
.auth-form-wrapper *[style*="color: #333"] {
  color: var(--light-gold-text) !important;
}

/* Responsive Design Adjustments */

/* Large screens (desktops) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .auth-form-wrapper {
    padding: 3rem !important;
  }

  .form-control {
    font-size: 16px;
    padding: 14px 18px;
  }
}

/* Additional login page fixes */
.auth-left-wrapper {
  background: var(--gold-gradient) !important;
}

.auth-left-wrapper-pic {
  background-color: var(--darker-bg) !important;
}

/* Ensure all login page text is visible */
.col-md-8 * {
  color: var(--light-gold-text) !important;
}

.col-md-8 label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
}

.col-md-8 h5 {
  color: var(--light-gold-text) !important;
}

/* Fix any remaining black text issues on login page */
.auth-page * {
  color: var(--light-gold-text) !important;
}

.auth-page label,
.auth-page h1,
.auth-page h2,
.auth-page h3,
.auth-page h4,
.auth-page h5,
.auth-page h6,
.auth-page p,
.auth-page span,
.auth-page div {
  color: var(--light-gold-text) !important;
}

/* Login form table styling (for older login forms) */
.login-form {
  color: var(--light-gold-text) !important;
}

.login-form td {
  color: var(--light-gold-text) !important;
  padding: 10px !important;
}

.login-form input[type="text"],
.login-form input[type="password"] {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
}

.login-form button {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--darker-bg) !important;
  border-radius: 8px !important;
  padding: 10px 20px !important;
  font-weight: 600 !important;
}

.login-form button:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3) !important;
}

/* Specific fixes for login page text visibility */
.text-muted {
  color: var(--light-gold-text) !important;
}

.auth-form-wrapper .text-muted {
  color: var(--light-gold-text) !important;
}

.auth-form-wrapper h5.text-muted {
  color: var(--light-gold-text) !important;
  font-weight: 600 !important;
}

.auth-form-wrapper a.text-muted {
  color: var(--primary-gold) !important;
}

.auth-form-wrapper a.text-muted:hover {
  color: var(--gold-hover) !important;
}

/* Force all login form text to be visible */
.col-md-8 .auth-form-wrapper * {
  color: var(--light-gold-text) !important;
}

.col-md-8 .auth-form-wrapper label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
}

.col-md-8 .auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
}

.col-md-8 .auth-form-wrapper a {
  color: var(--primary-gold) !important;
}

/* Override Bootstrap text utilities */
.text-dark,
.text-black,
.text-secondary {
  color: var(--light-gold-text) !important;
}

/* Specific targeting for login form elements */
.forms-sample label {
  color: var(--light-gold-text) !important;
}

.forms-sample .form-group label {
  color: var(--light-gold-text) !important;
}

/* Make sure all text in the login area is golden */
div[class*="col-md-8"] * {
  color: var(--light-gold-text) !important;
}

div[class*="col-md-8"] a {
  color: var(--primary-gold) !important;
}

div[class*="col-md-8"] a:hover {
  color: var(--gold-hover) !important;
}

/* Final override for any stubborn black text */
.auth-form-wrapper *:not(.btn):not(.form-control) {
  color: var(--light-gold-text) !important;
}

/* CRITICAL FIX: Force all input text to be golden */
input[type="text"],
input[type="password"],
input[type="email"],
textarea,
select {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 1) !important;
  border-color: var(--primary-gold) !important;
}

/* Specific targeting for login form inputs */
#username,
#password,
input[name="login_username"],
input[name="login_password"] {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 10px !important;
  padding: 15px 20px !important;
  font-size: 16px !important;
}

#username:focus,
#password:focus,
input[name="login_username"]:focus,
input[name="login_password"]:focus {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 1) !important;
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.4) !important;
}

/* Force all labels to be golden */
label[for="username"],
label[for="password"],
.form-group label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  display: block !important;
}

/* Override any inline styles or other CSS that might set text to black */
.col-md-8 * {
  color: var(--light-gold-text) !important;
}

.col-md-8 input {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
}

.col-md-8 label {
  color: var(--light-gold-text) !important;
}

/* Ensure welcome text is golden */
.col-md-8 h5,
.col-md-8 .text-muted {
  color: var(--light-gold-text) !important;
}

/* Force all form elements to have golden text */
.forms-sample * {
  color: var(--light-gold-text) !important;
}

.forms-sample input {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
}

.forms-sample label {
  color: var(--light-gold-text) !important;
}

/* Nuclear option: Override everything in the login area */
.auth-page *,
.auth-page input,
.auth-page label,
.auth-page h1,
.auth-page h2,
.auth-page h3,
.auth-page h4,
.auth-page h5,
.auth-page h6,
.auth-page p,
.auth-page span,
.auth-page div {
  color: var(--light-gold-text) !important;
}

.auth-page input[type="text"],
.auth-page input[type="password"] {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
}

/* Specific override for any remaining black text */
* {
  color: inherit;
}

body * {
  color: var(--light-gold-text);
}

/* Override Bootstrap and other framework defaults */
.text-dark,
.text-black,
.text-secondary,
.text-muted {
  color: var(--light-gold-text) !important;
}

/* Make sure placeholder text is visible */
::placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

::-webkit-input-placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

::-moz-placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

:-ms-input-placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

/* ULTIMATE OVERRIDE: Force golden theme over all other styles */
body {
  color: var(--light-gold-text) !important;
  background: var(--gold-gradient) !important;
}

.main-wrapper {
  color: var(--light-gold-text) !important;
}

.page-wrapper {
  color: var(--light-gold-text) !important;
}

.page-content {
  color: var(--light-gold-text) !important;
}

/* Override Bootstrap text utilities with maximum specificity */
.text-body,
.text-black,
.text-dark,
.text-secondary,
.text-muted {
  color: var(--light-gold-text) !important;
}

/* Force all form elements to use golden colors */
input,
textarea,
select,
.form-control {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border-color: var(--gold-border) !important;
}

input:focus,
textarea:focus,
select:focus,
.form-control:focus {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 1) !important;
  border-color: var(--primary-gold) !important;
}

/* Specific login page overrides */
.auth-page,
.auth-page *,
.auth-form-wrapper,
.auth-form-wrapper * {
  color: var(--light-gold-text) !important;
}

.auth-page input,
.auth-form-wrapper input {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
}

.auth-page label,
.auth-form-wrapper label {
  color: var(--light-gold-text) !important;
}

/* Override any inline styles */
[style*="color: #000"],
[style*="color: black"],
[style*="color: #222"],
[style*="color: #333"] {
  color: var(--light-gold-text) !important;
}

/* Force all text elements to be golden */
h1, h2, h3, h4, h5, h6,
p, span, div, label,
a, strong, em, i, b {
  color: var(--light-gold-text) !important;
}

/* Links should be golden with hover effect */
a {
  color: var(--primary-gold) !important;
}

a:hover {
  color: var(--gold-hover) !important;
}

/* Ensure all login form elements are properly styled */
.col-md-8 .auth-form-wrapper input[type="text"],
.col-md-8 .auth-form-wrapper input[type="password"] {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 10px !important;
  padding: 15px 20px !important;
  font-size: 16px !important;
}

.col-md-8 .auth-form-wrapper input[type="text"]:focus,
.col-md-8 .auth-form-wrapper input[type="password"]:focus {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 1) !important;
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.4) !important;
}

/* Final nuclear option - override everything */
* {
  color: inherit !important;
}

html {
  color: var(--light-gold-text) !important;
}

body * {
  color: var(--light-gold-text) !important;
}

/* Specific targeting for the exact login elements */
input#username,
input#password,
input[name="login_username"],
input[name="login_password"] {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
}

label[for="username"],
label[for="password"] {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
}

/* CRITICAL: Override style.css specific problematic rules */
.main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper * {
  color: var(--light-gold-text) !important;
}

.main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper input {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
}

.main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper label {
  color: var(--light-gold-text) !important;
}

.main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
}

/* Override the specific Bootstrap classes that cause black text */
.main-wrapper .text-body {
  color: var(--light-gold-text) !important;
}

.main-wrapper .text-black {
  color: var(--light-gold-text) !important;
}

.main-wrapper .text-dark {
  color: var(--light-gold-text) !important;
}

.main-wrapper .text-secondary {
  color: var(--light-gold-text) !important;
}

.main-wrapper .text-muted {
  color: var(--light-gold-text) !important;
}

/* Force input text color with maximum specificity */
.main-wrapper .page-wrapper .page-content input[type="text"],
.main-wrapper .page-wrapper .page-content input[type="password"] {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
}

/* Ensure typed text is visible */
.main-wrapper .page-wrapper .page-content input[type="text"]:focus,
.main-wrapper .page-wrapper .page-content input[type="password"]:focus {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 1) !important;
}

/* Target the exact login form structure */
.main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper .forms-sample input {
  color: var(--light-gold-text) !important;
  background: rgba(13, 13, 13, 0.9) !important;
}

.main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper .forms-sample label {
  color: var(--light-gold-text) !important;
}

/* Override any remaining body color inheritance */
.main-wrapper body,
.main-wrapper * {
  color: var(--light-gold-text) !important;
}

/* USER DASHBOARD STYLING - Fix black/white text issues */

/* Main dashboard content */
.page-content {
  background: rgba(26, 26, 26, 0.8) !important;
  color: var(--light-gold-text) !important;
}

/* Dashboard cards and panels */
.card,
.panel,
.widget {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1) !important;
}

.card-header,
.panel-heading,
.widget-header {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  border-bottom: 2px solid var(--gold-border) !important;
  font-weight: bold !important;
}

.card-body,
.panel-body,
.widget-body {
  color: var(--light-gold-text) !important;
  background: transparent !important;
}

.card-title,
.panel-title,
.widget-title {
  color: var(--darker-bg) !important;
  font-weight: bold !important;
}

/* Dashboard text elements */
.page-content h1,
.page-content h2,
.page-content h3,
.page-content h4,
.page-content h5,
.page-content h6 {
  color: var(--primary-gold) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

.page-content p,
.page-content span,
.page-content div,
.page-content label {
  color: var(--light-gold-text) !important;
}

/* Dashboard links */
.page-content a {
  color: var(--primary-gold) !important;
  text-decoration: none !important;
}

.page-content a:hover {
  color: var(--gold-hover) !important;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5) !important;
}

/* Dashboard buttons */
.page-content .btn {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--darker-bg) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  transition: all 0.3s ease !important;
}

.page-content .btn:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3) !important;
}

/* Dashboard forms */
.page-content .form-control {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 8px !important;
}

.page-content .form-control:focus {
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.4) !important;
  background: rgba(13, 13, 13, 1) !important;
}

.page-content .form-group label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
}

/* Dashboard tables */
.page-content .table {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
}

.page-content .table thead th {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  border-color: var(--gold-border) !important;
  font-weight: bold !important;
}

.page-content .table tbody td {
  color: var(--light-gold-text) !important;
  border-color: var(--gold-border) !important;
}

.page-content .table tbody tr:hover {
  background: rgba(212, 175, 55, 0.1) !important;
}

/* Dashboard navigation */
.horizontal-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border-bottom: 2px solid var(--gold-border) !important;
}

.horizontal-menu .navbar-nav .nav-link {
  color: var(--light-gold-text) !important;
}

.horizontal-menu .navbar-nav .nav-link:hover {
  color: var(--primary-gold) !important;
  background: rgba(212, 175, 55, 0.2) !important;
}

.horizontal-menu .navbar-nav .nav-item.active .nav-link {
  color: var(--primary-gold) !important;
  background: rgba(212, 175, 55, 0.2) !important;
}

/* Dashboard footer */
.footer {
  background: linear-gradient(135deg, rgba(13, 13, 13, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%) !important;
  border-top: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.footer p,
.footer small {
  color: var(--light-gold-text) !important;
}

/* COMPREHENSIVE DASHBOARD TEXT FIXES */

/* Override all possible black text sources */
.main-wrapper .page-wrapper .page-content * {
  color: var(--light-gold-text) !important;
}

.main-wrapper .page-wrapper .page-content h1,
.main-wrapper .page-wrapper .page-content h2,
.main-wrapper .page-wrapper .page-content h3,
.main-wrapper .page-wrapper .page-content h4,
.main-wrapper .page-wrapper .page-content h5,
.main-wrapper .page-wrapper .page-content h6 {
  color: var(--primary-gold) !important;
}

.main-wrapper .page-wrapper .page-content a {
  color: var(--primary-gold) !important;
}

/* Dashboard specific elements */
.dashboard-content,
.usercp-content,
.account-content {
  background: rgba(26, 26, 26, 0.8) !important;
  color: var(--light-gold-text) !important;
}

/* Account balance and stats */
.account-balance,
.stats-widget,
.info-widget {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

/* Progress bars */
.progress {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 1px solid var(--gold-border) !important;
}

.progress-bar {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
}

/* Badges and labels */
.badge,
.label {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
}

/* List groups */
.list-group-item {
  background: rgba(26, 26, 26, 0.9) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.list-group-item:hover {
  background: rgba(212, 175, 55, 0.1) !important;
}

/* Alerts and notifications */
.alert {
  border: 1px solid var(--gold-border) !important;
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
}

/* Tabs */
.nav-tabs .nav-link {
  color: var(--light-gold-text) !important;
  background: rgba(26, 26, 26, 0.9) !important;
  border: 1px solid var(--gold-border) !important;
}

.nav-tabs .nav-link.active {
  color: var(--darker-bg) !important;
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border-color: var(--gold-border) !important;
}

/* Modal dialogs */
.modal-content {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.modal-header {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border-bottom: 2px solid var(--gold-border) !important;
}

.modal-title {
  color: var(--darker-bg) !important;
}

.modal-body {
  color: var(--light-gold-text) !important;
}

/* Override Bootstrap utilities that cause black text */
.main-wrapper .text-body,
.main-wrapper .text-black,
.main-wrapper .text-dark,
.main-wrapper .text-secondary {
  color: var(--light-gold-text) !important;
}

.main-wrapper .text-muted {
  color: rgba(244, 228, 166, 0.8) !important;
}

/* Force all dashboard content to use golden theme */
.main-wrapper .page-wrapper .page-content .container *,
.main-wrapper .page-wrapper .page-content .row *,
.main-wrapper .page-wrapper .page-content .col-* * {
  color: var(--light-gold-text) !important;
}

/* Specific dashboard page elements */
.dashboard-stats,
.account-info,
.character-info,
.server-info {
  background: rgba(26, 26, 26, 0.9) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 10px !important;
  padding: 20px !important;
}

/* ========================================
   ULTIMATE OVERRIDE SECTION
   This section has maximum CSS specificity
   to override ANY other styles
   ======================================== */

/* Force body and html to use golden theme */
html,
html body,
html body.main-content {
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 25%, #6b3e2a 50%, #8b5a3c 75%, #a67c52 100%) !important;
  color: #f4e4a6 !important;
}

/* Force all main wrapper content */
html body .main-wrapper,
html body .main-wrapper .page-wrapper,
html body .main-wrapper .page-wrapper .page-content {
  background: transparent !important;
  color: #f4e4a6 !important;
}

/* Force all text elements to be golden */
html body .main-wrapper .page-wrapper .page-content *,
html body .main-wrapper .page-wrapper .page-content h1,
html body .main-wrapper .page-wrapper .page-content h2,
html body .main-wrapper .page-wrapper .page-content h3,
html body .main-wrapper .page-wrapper .page-content h4,
html body .main-wrapper .page-wrapper .page-content h5,
html body .main-wrapper .page-wrapper .page-content h6,
html body .main-wrapper .page-wrapper .page-content p,
html body .main-wrapper .page-wrapper .page-content span,
html body .main-wrapper .page-wrapper .page-content div,
html body .main-wrapper .page-wrapper .page-content label,
html body .main-wrapper .page-wrapper .page-content td,
html body .main-wrapper .page-wrapper .page-content th {
  color: #f4e4a6 !important;
}

/* Force headers to be primary gold */
html body .main-wrapper .page-wrapper .page-content h1,
html body .main-wrapper .page-wrapper .page-content h2,
html body .main-wrapper .page-wrapper .page-content h3,
html body .main-wrapper .page-wrapper .page-content h4,
html body .main-wrapper .page-wrapper .page-content h5,
html body .main-wrapper .page-wrapper .page-content h6 {
  color: #d4af37 !important;
}

/* Force links to be golden */
html body .main-wrapper .page-wrapper .page-content a {
  color: #d4af37 !important;
}

/* Force all cards and panels to have dark backgrounds */
html body .main-wrapper .page-wrapper .page-content .card,
html body .main-wrapper .page-wrapper .page-content .panel,
html body .main-wrapper .page-wrapper .page-content .widget {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

/* Force card headers to be golden */
html body .main-wrapper .page-wrapper .page-content .card-header,
html body .main-wrapper .page-wrapper .page-content .panel-heading,
html body .main-wrapper .page-wrapper .page-content .widget-header {
  background: linear-gradient(135deg, #b8941f 0%, #d4af37 100%) !important;
  color: #0d0d0d !important;
  border-bottom: 2px solid #8b5a3c !important;
}

/* Force all form elements */
html body .main-wrapper .page-wrapper .page-content input,
html body .main-wrapper .page-wrapper .page-content textarea,
html body .main-wrapper .page-wrapper .page-content select,
html body .main-wrapper .page-wrapper .page-content .form-control {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

/* Force all buttons */
html body .main-wrapper .page-wrapper .page-content .btn,
html body .main-wrapper .page-wrapper .page-content button {
  background: linear-gradient(135deg, #b8941f 0%, #d4af37 100%) !important;
  border: 2px solid #8b5a3c !important;
  color: #0d0d0d !important;
}

/* Force all tables */
html body .main-wrapper .page-wrapper .page-content .table,
html body .main-wrapper .page-wrapper .page-content table {
  background: rgba(26, 26, 26, 0.9) !important;
  color: #f4e4a6 !important;
}

html body .main-wrapper .page-wrapper .page-content .table thead th,
html body .main-wrapper .page-wrapper .page-content table thead th {
  background: linear-gradient(135deg, #b8941f 0%, #d4af37 100%) !important;
  color: #0d0d0d !important;
  border-color: #8b5a3c !important;
}

html body .main-wrapper .page-wrapper .page-content .table tbody td,
html body .main-wrapper .page-wrapper .page-content table tbody td {
  color: #f4e4a6 !important;
  border-color: #8b5a3c !important;
}

/* Override Bootstrap text utilities with maximum specificity */
html body .main-wrapper .page-wrapper .page-content .text-body,
html body .main-wrapper .page-wrapper .page-content .text-black,
html body .main-wrapper .page-wrapper .page-content .text-dark,
html body .main-wrapper .page-wrapper .page-content .text-secondary {
  color: #f4e4a6 !important;
}

html body .main-wrapper .page-wrapper .page-content .text-muted {
  color: rgba(244, 228, 166, 0.8) !important;
}

/* Force navigation elements */
html body .main-wrapper .horizontal-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border-bottom: 2px solid #8b5a3c !important;
}

html body .main-wrapper .horizontal-menu .navbar-nav .nav-link {
  color: #f4e4a6 !important;
}

html body .main-wrapper .horizontal-menu .navbar-nav .nav-link:hover {
  color: #d4af37 !important;
}

/* Force footer */
html body .main-wrapper .page-wrapper .footer {
  background: linear-gradient(135deg, rgba(13, 13, 13, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%) !important;
  border-top: 2px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

html body .main-wrapper .page-wrapper .footer * {
  color: #f4e4a6 !important;
}

/* ========================================
   WHITE BACKGROUND FIXES
   Remove all white backgrounds and replace with golden theme
   ======================================== */

/* Force all white backgrounds to be dark golden */
html body .main-wrapper .page-wrapper .page-content [style*="background-color: #fff"],
html body .main-wrapper .page-wrapper .page-content [style*="background-color: white"],
html body .main-wrapper .page-wrapper .page-content [style*="background: #fff"],
html body .main-wrapper .page-wrapper .page-content [style*="background: white"] {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
}

/* Override any CSS classes that set white backgrounds */
html body .main-wrapper .page-wrapper .page-content .bg-white,
html body .main-wrapper .page-wrapper .page-content .background-white,
html body .main-wrapper .page-wrapper .page-content .white-bg {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
}

/* Force all containers and rows to have dark backgrounds */
html body .main-wrapper .page-wrapper .page-content .container,
html body .main-wrapper .page-wrapper .page-content .container-fluid,
html body .main-wrapper .page-wrapper .page-content .row,
html body .main-wrapper .page-wrapper .page-content .col,
html body .main-wrapper .page-wrapper .page-content [class*="col-"] {
  background: transparent !important;
}

/* Force all dashboard widgets and info boxes */
html body .main-wrapper .page-wrapper .page-content .info-box,
html body .main-wrapper .page-wrapper .page-content .widget-box,
html body .main-wrapper .page-wrapper .page-content .dashboard-widget,
html body .main-wrapper .page-wrapper .page-content .stats-box,
html body .main-wrapper .page-wrapper .page-content .account-box,
html body .main-wrapper .page-wrapper .page-content .balance-box {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

/* Force all content areas */
html body .main-wrapper .page-wrapper .page-content .content,
html body .main-wrapper .page-wrapper .page-content .main-content,
html body .main-wrapper .page-wrapper .page-content .dashboard-content {
  background: transparent !important;
  color: #f4e4a6 !important;
}

/* Force all list items and menu items */
html body .main-wrapper .page-wrapper .page-content .list-group-item,
html body .main-wrapper .page-wrapper .page-content .menu-item,
html body .main-wrapper .page-wrapper .page-content .nav-item {
  background: rgba(26, 26, 26, 0.9) !important;
  border: 1px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

/* Force all wells and panels */
html body .main-wrapper .page-wrapper .page-content .well,
html body .main-wrapper .page-wrapper .page-content .panel-default,
html body .main-wrapper .page-wrapper .page-content .panel-white {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

/* Force all modal and popup backgrounds */
html body .main-wrapper .page-wrapper .page-content .modal-body,
html body .main-wrapper .page-wrapper .page-content .popup-content,
html body .main-wrapper .page-wrapper .page-content .dropdown-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid #8b5a3c !important;
  color: #f4e4a6 !important;
}

/* Force all tab content */
html body .main-wrapper .page-wrapper .page-content .tab-content,
html body .main-wrapper .page-wrapper .page-content .tab-pane {
  background: transparent !important;
  color: #f4e4a6 !important;
}

/* Force all accordion and collapse content */
html body .main-wrapper .page-wrapper .page-content .collapse,
html body .main-wrapper .page-wrapper .page-content .accordion-body {
  background: rgba(26, 26, 26, 0.9) !important;
  color: #f4e4a6 !important;
}

/* Override any remaining white backgrounds with CSS selectors */
html body .main-wrapper .page-wrapper .page-content *[style*="background:#fff"],
html body .main-wrapper .page-wrapper .page-content *[style*="background: #fff"],
html body .main-wrapper .page-wrapper .page-content *[style*="background-color:#fff"],
html body .main-wrapper .page-wrapper .page-content *[style*="background-color: #fff"],
html body .main-wrapper .page-wrapper .page-content *[style*="background:#ffffff"],
html body .main-wrapper .page-wrapper .page-content *[style*="background-color:#ffffff"] {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
}

/* Force specific dashboard elements that might have white backgrounds */
html body .main-wrapper .page-wrapper .page-content .card-body,
html body .main-wrapper .page-wrapper .page-content .panel-body,
html body .main-wrapper .page-wrapper .page-content .widget-body {
  background: transparent !important;
  color: #f4e4a6 !important;
}

/* Ensure proper contrast for any remaining elements */
html body .main-wrapper .page-wrapper .page-content div[style*="background-color: rgb(255, 255, 255)"],
html body .main-wrapper .page-wrapper .page-content div[style*="background: rgb(255, 255, 255)"] {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: #f4e4a6 !important;
}

/* ========================================
   TOP NAVIGATION BAR STYLING
   Fix the top tabs (Dashboard, Password, Characters, etc.)
   ======================================== */

/* Main navigation bar - be more specific to avoid conflicts */
.main-wrapper .horizontal-menu .navbar {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border-bottom: 2px solid #8b5a3c !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
}

/* Navigation links only - don't break layout */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .nav-link {
  color: #f4e4a6 !important;
  transition: all 0.3s ease !important;
}

/* Navigation links hover */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .nav-link:hover {
  color: #d4af37 !important;
  background: rgba(212, 175, 55, 0.2) !important;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5) !important;
}

/* Active navigation item */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item.active .nav-link {
  color: #0d0d0d !important;
  background: linear-gradient(135deg, #b8941f 0%, #d4af37 100%) !important;
  font-weight: bold !important;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Navigation menu titles and icons */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .nav-link .menu-title,
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .nav-link .link-icon {
  color: inherit !important;
}

/* Dropdown menus in navigation */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .dropdown-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%) !important;
  border: 2px solid #8b5a3c !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
}

/* Dropdown items */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .dropdown-menu .dropdown-item {
  color: #f4e4a6 !important;
  background: transparent !important;
}

/* Dropdown items hover */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .dropdown-menu .dropdown-item:hover {
  color: #d4af37 !important;
  background: rgba(212, 175, 55, 0.2) !important;
}

/* Language flag icons */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .nav-link .flag-icon {
  filter: brightness(1.2) !important;
}

/* User profile dropdown */
.main-wrapper .horizontal-menu .navbar .navbar-nav .nav-item .nav-link .profile-pic {
  border: 2px solid #8b5a3c !important;
  filter: brightness(1.1) !important;
}

/* EMERGENCY OVERRIDE: Force all possible selectors */
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper input,
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper input[type="text"],
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper input[type="password"],
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper #username,
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper #password {
  color: #f4e4a6 !important;
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid #8b5a3c !important;
  border-radius: 10px !important;
  padding: 15px 20px !important;
  font-size: 16px !important;
}

html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper label,
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper label[for="username"],
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper label[for="password"] {
  color: #f4e4a6 !important;
  font-weight: 500 !important;
}

html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper h5,
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper .text-muted {
  color: #f4e4a6 !important;
}

/* Force all text in the login area to be golden */
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper *:not(.btn) {
  color: #f4e4a6 !important;
}

/* Override any possible inline styles */
html body .main-wrapper .page-wrapper .page-content .auth-page .col-md-8 .auth-form-wrapper [style] {
  color: #f4e4a6 !important;
}

/* Medium screens (tablets) */
@media (max-width: 991.98px) {
  .navbar-nav {
    background: rgba(26, 26, 26, 0.98);
    border-radius: 8px;
    margin-top: 10px;
    padding: 10px;
  }

  .page-navigation .nav-link {
    padding: 8px 12px;
    font-size: 14px;
  }

  .auth-form-wrapper {
    padding: 2rem 1.5rem;
    margin: 10px;
  }

  .card {
    margin: 10px 5px;
  }

  .hero-caption h1 {
    font-size: 2.5rem;
  }
}

/* Small screens (phones) */
@media (max-width: 767.98px) {
  :root {
    --gold-gradient: linear-gradient(180deg, #2c1810 0%, #4a2c1a 25%, #6b3e2a 50%, #8b5a3c 75%, #a67c52 100%);
  }

  .navbar-brand img {
    width: 120px;
    height: auto;
  }

  .page-navigation {
    flex-direction: column;
    gap: 5px;
  }

  .page-navigation .nav-item {
    width: 100%;
    margin: 2px 0;
  }

  .page-navigation .nav-link {
    justify-content: center;
    padding: 12px;
    text-align: center;
  }

  .auth-form-wrapper {
    padding: 1.5rem 1rem;
    margin: 5px;
    border-radius: 8px;
  }

  .form-control {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 14px;
  }

  .btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
  }

  .card {
    margin: 5px 0;
    border-radius: 8px;
  }

  .hero-caption h1 {
    font-size: 2rem;
    text-align: center;
  }

  .table-responsive {
    border: 1px solid var(--gold-border);
    border-radius: 8px;
  }

  .modal-dialog {
    margin: 10px;
  }

  .modal-content {
    border-radius: 8px;
  }
}

/* Extra small screens */
@media (max-width: 575.98px) {
  .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  .auth-form-wrapper {
    padding: 1rem;
  }

  .navbar-brand img {
    width: 100px;
  }

  .hero-caption h1 {
    font-size: 1.8rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .page-navigation .nav-link {
    font-size: 13px;
    padding: 10px;
  }

  .card-header {
    padding: 10px 15px;
    font-size: 14px;
  }

  .card-body {
    padding: 15px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .nav-link:hover {
    transform: none;
  }

  .btn:hover {
    transform: none;
  }

  .form-control:hover {
    transform: none;
  }

  /* Larger touch targets */
  .nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .btn {
    min-height: 44px;
  }

  .form-check-input {
    width: 20px;
    height: 20px;
  }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .golden-texture {
    background-size: 100px 100px;
  }
}

/* Dark mode preference support */
@media (prefers-color-scheme: dark) {
  /* The theme is already dark, but we can enhance it */
  :root {
    --gold-gradient: linear-gradient(135deg, #1a1a1a 0%, #2c1810 25%, #4a2c1a 50%, #6b3e2a 75%, #8b5a3c 100%);
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .nav-link:hover {
    transform: none;
  }

  .btn:hover {
    transform: none;
  }

  .form-control:focus {
    transform: none;
  }
}
