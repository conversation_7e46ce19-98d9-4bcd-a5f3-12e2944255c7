/* Golden Theme Enhancements */

/* Background texture overlay */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../img/golden-texture.svg') repeat;
  opacity: 0.3;
  z-index: -1;
  pointer-events: none;
}

/* Additional golden styling for cards and containers */
.card {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border: 1px solid var(--gold-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.card-header {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  color: var(--darker-bg);
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  border-bottom: 2px solid var(--gold-border);
}

.card-body {
  color: var(--light-gold-text);
}

/* Table styling */
.table {
  background: rgba(26, 26, 26, 0.9);
  color: var(--light-gold-text);
}

.table thead th {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  color: var(--darker-bg);
  border-color: var(--gold-border);
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.table tbody tr {
  border-color: var(--gold-border);
}

.table tbody tr:hover {
  background: rgba(212, 175, 55, 0.1);
}

/* Navigation enhancements */
.navbar, .top-navbar, .bottom-navbar {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border-bottom: 2px solid var(--gold-border);
  backdrop-filter: blur(10px);
}

.navbar-brand {
  color: var(--primary-gold) !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.navbar-brand img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);
}

.nav-link {
  color: var(--light-gold-text) !important;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-gold) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  transform: translateY(-1px);
}

.nav-link:hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
}

.nav-item.active .nav-link {
  color: var(--primary-gold) !important;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 5px;
  box-shadow: inset 0 0 10px rgba(212, 175, 55, 0.3);
}

.navbar-toggler {
  border-color: var(--gold-border);
  color: var(--primary-gold);
}

.navbar-toggler:hover {
  background: rgba(212, 175, 55, 0.2);
}

/* Page navigation specific styling */
.page-navigation {
  padding: 10px 0;
}

.page-navigation .nav-item {
  margin: 0 5px;
}

.page-navigation .nav-link {
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-navigation .nav-link .link-icon {
  width: 16px;
  height: 16px;
}

.page-navigation .nav-item:hover .nav-link {
  background: rgba(212, 175, 55, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Form enhancements */
.form-control {
  background: rgba(13, 13, 13, 0.9);
  border: 2px solid var(--gold-border);
  color: var(--light-gold-text);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.4), inset 0 0 5px rgba(212, 175, 55, 0.1);
  background: rgba(13, 13, 13, 1);
  outline: none;
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: var(--dark-gold);
  background: rgba(13, 13, 13, 0.95);
}

.form-control::placeholder {
  color: rgba(244, 228, 166, 0.6);
  font-style: italic;
}

.form-control:invalid {
  border-color: #f44336;
  box-shadow: 0 0 5px rgba(244, 67, 54, 0.3);
}

.form-control:valid {
  border-color: var(--primary-gold);
}

/* Form group styling */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  color: var(--light-gold-text) !important;
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Form text and help text */
.form-text {
  color: rgba(244, 228, 166, 0.8) !important;
  font-size: 12px;
  margin-top: 5px;
  font-style: italic;
}

/* Checkbox styling */
.form-check {
  margin: 1rem 0;
}

.form-check-label {
  color: var(--light-gold-text) !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  background: rgba(13, 13, 13, 0.9);
  border: 2px solid var(--gold-border);
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  transition: all 0.3s ease;
}

.form-check-input:checked {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-color: var(--primary-gold);
}

.form-check-input:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--darker-bg);
  font-weight: bold;
  font-size: 12px;
}

.form-check-input:focus {
  box-shadow: 0 0 5px var(--primary-gold);
  outline: none;
}

/* Forms sample class */
.forms-sample {
  background: transparent;
}

/* Submit button in forms */
.forms-sample .btn {
  margin-top: 1rem;
  min-width: 150px;
}

/* Alert styling */
.alert {
  border: 1px solid var(--gold-border);
  background: rgba(26, 26, 26, 0.9);
  color: var(--light-gold-text);
}

.alert-success {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
  color: #a5d6a7;
}

.alert-danger {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #ef9a9a;
}

.alert-warning {
  background: rgba(255, 193, 7, 0.2);
  border-color: var(--primary-gold);
  color: var(--light-gold-text);
}

/* Badge styling */
.badge {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  color: var(--darker-bg);
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Pagination styling */
.pagination .page-link {
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid var(--gold-border);
  color: var(--light-gold-text);
}

.pagination .page-link:hover {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-color: var(--gold-border);
  color: var(--darker-bg);
}

/* Modal styling */
.modal-content {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%);
  border: 2px solid var(--gold-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.modal-header {
  border-bottom: 2px solid var(--gold-border);
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
}

.modal-title {
  color: var(--darker-bg);
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.modal-body {
  color: var(--light-gold-text);
}

.modal-footer {
  border-top: 1px solid var(--gold-border);
}

/* Dropdown styling */
.dropdown-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%);
  border: 1px solid var(--gold-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  color: var(--light-gold-text);
}

.dropdown-item:hover {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
}

/* Progress bar styling */
.progress {
  background: rgba(13, 13, 13, 0.9);
  border: 1px solid var(--gold-border);
}

.progress-bar {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
}

/* List group styling */
.list-group-item {
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid var(--gold-border);
  color: var(--light-gold-text);
}

.list-group-item:hover {
  background: rgba(212, 175, 55, 0.1);
}

.list-group-item.active {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-color: var(--gold-border);
  color: var(--darker-bg);
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%);
}

/* Text selection styling */
::selection {
  background: var(--primary-gold);
  color: var(--darker-bg);
}

::-moz-selection {
  background: var(--primary-gold);
  color: var(--darker-bg);
}

/* Footer styling */
.footer {
  background: linear-gradient(135deg, rgba(13, 13, 13, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%);
  border-top: 2px solid var(--gold-border);
  color: var(--light-gold-text);
}

/* Additional utility classes */
.text-gold {
  color: var(--primary-gold) !important;
}

.text-light-gold {
  color: var(--light-gold-text) !important;
}

.text-dark-gold {
  color: var(--dark-gold) !important;
}

.bg-gold {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
}

.bg-dark-gold {
  background: var(--dark-bg) !important;
}

.border-gold {
  border-color: var(--gold-border) !important;
}

/* Content area specific styling */
.auth-form-wrapper {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border: 1px solid var(--gold-border);
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
  backdrop-filter: blur(10px);
}

.auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
}

.noble-ui-logo img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);
}

/* Hero section styling */
#hero-section {
  position: relative;
  overflow: hidden;
}

.hero-overlay {
  background: linear-gradient(135deg, rgba(44, 24, 16, 0.7) 0%, rgba(26, 26, 26, 0.8) 100%);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-caption {
  position: relative;
  z-index: 2;
  color: var(--light-gold-text);
}

.hero-caption h1 {
  color: var(--primary-gold);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

/* Container and content styling */
.container {
  position: relative;
  z-index: 1;
}

/* Form labels */
label {
  color: var(--light-gold-text) !important;
  font-weight: 500;
}

/* Heading styling */
.heading h1 {
  color: var(--primary-gold);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.colored {
  color: var(--gold-hover) !important;
}

/* Animation elements */
.animation-element {
  opacity: 0.9;
}

/* HR styling */
hr {
  border-color: var(--gold-border);
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
  height: 2px;
  border: none;
}

/* Text muted override */
.text-muted {
  color: rgba(244, 228, 166, 0.7) !important;
}

/* Font weight overrides */
.font-weight-normal {
  color: var(--light-gold-text);
}

.font-weight-medium {
  color: var(--light-gold-text);
}

/* Small text styling */
small {
  color: rgba(244, 228, 166, 0.8);
}

/* Link styling in content areas */
.auth-form-wrapper a {
  color: var(--primary-gold);
  text-decoration: none;
}

.auth-form-wrapper a:hover {
  color: var(--gold-hover);
  text-decoration: underline;
}

/* Video overlay for hero sections */
#bgvid {
  filter: brightness(0.7) sepia(0.3) hue-rotate(35deg);
}

/* Page content wrapper */
.page-content {
  background: rgba(26, 26, 26, 0.3);
  min-height: calc(100vh - 200px);
  backdrop-filter: blur(5px);
}

/* Main wrapper styling */
.main-wrapper {
  background: var(--gold-gradient);
  min-height: 100vh;
}

/* Additional content containers */
.content-wrapper {
  background: rgba(26, 26, 26, 0.8);
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
  border: 1px solid var(--gold-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Login Page Specific Styling */
.auth-form-wrapper {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 20px rgba(212, 175, 55, 0.15) !important;
  backdrop-filter: blur(15px) !important;
  padding: 2rem !important;
}

.auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
  font-weight: 600 !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

.auth-form-wrapper .noble-ui-logo img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5) !important;
}

/* Login form labels */
.auth-form-wrapper label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  display: block !important;
}

/* Login form inputs */
.auth-form-wrapper .form-control {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 10px !important;
  padding: 15px 20px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.auth-form-wrapper .form-control:focus {
  border-color: var(--primary-gold) !important;
  box-shadow:
    0 0 15px rgba(212, 175, 55, 0.4),
    inset 0 0 10px rgba(212, 175, 55, 0.1) !important;
  background: rgba(13, 13, 13, 1) !important;
  transform: translateY(-2px) !important;
}

.auth-form-wrapper .form-control::placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

/* Login button */
.auth-form-wrapper .btn-primary {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 50%, var(--gold-hover) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 12px !important;
  color: var(--darker-bg) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  padding: 12px 30px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3) !important;
}

.auth-form-wrapper .btn-primary:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4) !important;
  border-color: var(--gold-hover) !important;
}

/* Links in login form */
.auth-form-wrapper a {
  color: var(--primary-gold) !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

.auth-form-wrapper a:hover {
  color: var(--gold-hover) !important;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5) !important;
}

/* Form groups */
.auth-form-wrapper .form-group {
  margin-bottom: 1.5rem !important;
}

/* Text elements */
.auth-form-wrapper p,
.auth-form-wrapper span,
.auth-form-wrapper div {
  color: var(--light-gold-text) !important;
}

/* Override any remaining black text */
.auth-form-wrapper * {
  color: inherit !important;
}

.auth-form-wrapper *[style*="color: #000"],
.auth-form-wrapper *[style*="color: black"],
.auth-form-wrapper *[style*="color: #222"],
.auth-form-wrapper *[style*="color: #333"] {
  color: var(--light-gold-text) !important;
}

/* Responsive Design Adjustments */

/* Large screens (desktops) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .auth-form-wrapper {
    padding: 3rem !important;
  }

  .form-control {
    font-size: 16px;
    padding: 14px 18px;
  }
}

/* Additional login page fixes */
.auth-left-wrapper {
  background: var(--gold-gradient) !important;
}

.auth-left-wrapper-pic {
  background-color: var(--darker-bg) !important;
}

/* Ensure all login page text is visible */
.col-md-8 * {
  color: var(--light-gold-text) !important;
}

.col-md-8 label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
}

.col-md-8 h5 {
  color: var(--light-gold-text) !important;
}

/* Fix any remaining black text issues on login page */
.auth-page * {
  color: var(--light-gold-text) !important;
}

.auth-page label,
.auth-page h1,
.auth-page h2,
.auth-page h3,
.auth-page h4,
.auth-page h5,
.auth-page h6,
.auth-page p,
.auth-page span,
.auth-page div {
  color: var(--light-gold-text) !important;
}

/* Login form table styling (for older login forms) */
.login-form {
  color: var(--light-gold-text) !important;
}

.login-form td {
  color: var(--light-gold-text) !important;
  padding: 10px !important;
}

.login-form input[type="text"],
.login-form input[type="password"] {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
}

.login-form button {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--darker-bg) !important;
  border-radius: 8px !important;
  padding: 10px 20px !important;
  font-weight: 600 !important;
}

.login-form button:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3) !important;
}

/* Specific fixes for login page text visibility */
.text-muted {
  color: var(--light-gold-text) !important;
}

.auth-form-wrapper .text-muted {
  color: var(--light-gold-text) !important;
}

.auth-form-wrapper h5.text-muted {
  color: var(--light-gold-text) !important;
  font-weight: 600 !important;
}

.auth-form-wrapper a.text-muted {
  color: var(--primary-gold) !important;
}

.auth-form-wrapper a.text-muted:hover {
  color: var(--gold-hover) !important;
}

/* Force all login form text to be visible */
.col-md-8 .auth-form-wrapper * {
  color: var(--light-gold-text) !important;
}

.col-md-8 .auth-form-wrapper label {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
}

.col-md-8 .auth-form-wrapper h5 {
  color: var(--light-gold-text) !important;
}

.col-md-8 .auth-form-wrapper a {
  color: var(--primary-gold) !important;
}

/* Override Bootstrap text utilities */
.text-dark,
.text-black,
.text-secondary {
  color: var(--light-gold-text) !important;
}

/* Specific targeting for login form elements */
.forms-sample label {
  color: var(--light-gold-text) !important;
}

.forms-sample .form-group label {
  color: var(--light-gold-text) !important;
}

/* Make sure all text in the login area is golden */
div[class*="col-md-8"] * {
  color: var(--light-gold-text) !important;
}

div[class*="col-md-8"] a {
  color: var(--primary-gold) !important;
}

div[class*="col-md-8"] a:hover {
  color: var(--gold-hover) !important;
}

/* Final override for any stubborn black text */
.auth-form-wrapper *:not(.btn):not(.form-control) {
  color: var(--light-gold-text) !important;
}

/* Medium screens (tablets) */
@media (max-width: 991.98px) {
  .navbar-nav {
    background: rgba(26, 26, 26, 0.98);
    border-radius: 8px;
    margin-top: 10px;
    padding: 10px;
  }

  .page-navigation .nav-link {
    padding: 8px 12px;
    font-size: 14px;
  }

  .auth-form-wrapper {
    padding: 2rem 1.5rem;
    margin: 10px;
  }

  .card {
    margin: 10px 5px;
  }

  .hero-caption h1 {
    font-size: 2.5rem;
  }
}

/* Small screens (phones) */
@media (max-width: 767.98px) {
  :root {
    --gold-gradient: linear-gradient(180deg, #2c1810 0%, #4a2c1a 25%, #6b3e2a 50%, #8b5a3c 75%, #a67c52 100%);
  }

  .navbar-brand img {
    width: 120px;
    height: auto;
  }

  .page-navigation {
    flex-direction: column;
    gap: 5px;
  }

  .page-navigation .nav-item {
    width: 100%;
    margin: 2px 0;
  }

  .page-navigation .nav-link {
    justify-content: center;
    padding: 12px;
    text-align: center;
  }

  .auth-form-wrapper {
    padding: 1.5rem 1rem;
    margin: 5px;
    border-radius: 8px;
  }

  .form-control {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 14px;
  }

  .btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
  }

  .card {
    margin: 5px 0;
    border-radius: 8px;
  }

  .hero-caption h1 {
    font-size: 2rem;
    text-align: center;
  }

  .table-responsive {
    border: 1px solid var(--gold-border);
    border-radius: 8px;
  }

  .modal-dialog {
    margin: 10px;
  }

  .modal-content {
    border-radius: 8px;
  }
}

/* Extra small screens */
@media (max-width: 575.98px) {
  .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  .auth-form-wrapper {
    padding: 1rem;
  }

  .navbar-brand img {
    width: 100px;
  }

  .hero-caption h1 {
    font-size: 1.8rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .page-navigation .nav-link {
    font-size: 13px;
    padding: 10px;
  }

  .card-header {
    padding: 10px 15px;
    font-size: 14px;
  }

  .card-body {
    padding: 15px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .nav-link:hover {
    transform: none;
  }

  .btn:hover {
    transform: none;
  }

  .form-control:hover {
    transform: none;
  }

  /* Larger touch targets */
  .nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .btn {
    min-height: 44px;
  }

  .form-check-input {
    width: 20px;
    height: 20px;
  }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .golden-texture {
    background-size: 100px 100px;
  }
}

/* Dark mode preference support */
@media (prefers-color-scheme: dark) {
  /* The theme is already dark, but we can enhance it */
  :root {
    --gold-gradient: linear-gradient(135deg, #1a1a1a 0%, #2c1810 25%, #4a2c1a 50%, #6b3e2a 75%, #8b5a3c 100%);
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .nav-link:hover {
    transform: none;
  }

  .btn:hover {
    transform: none;
  }

  .form-control:focus {
    transform: none;
  }
}
